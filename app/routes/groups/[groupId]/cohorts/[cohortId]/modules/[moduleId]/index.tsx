import type { Route } from "./+types/index";
import { useParams } from "react-router";

import CourseModule from "~/components/modules/CourseModule";
import EventsModule from "~/components/modules/EventsModule";
import LinksModule from "~/components/modules/LinksModule";
import { FeedModule } from "~/components/modules/FeedModule";

import { useMyCohortModule } from "~/lib/api/client-queries";
import { useCourse } from "~/lib/api/client-queries";

import type { Course } from "~/lib/api/types";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Module - Sphere` },
    { name: "description", content: "Module content" },
  ];
}

export default function ModulePage() {
  const urlParams = useParams();
  const { groupId, cohortId, moduleId } = urlParams as Record<string, string>;
  const paramProps = { groupId, cohortId, moduleId };

  // Fetch module details
  const {
    data: moduleResponse,
    isLoading,
    isError,
  } = useMyCohortModule(groupId!, cohortId!, moduleId!);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-900 text-zinc-400">
        Loading module…
      </div>
    );
  }

  if (isError || !moduleResponse) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-900 text-red-500">
        Failed to load module.
      </div>
    );
  }

  const module = moduleResponse.data;

  // Render per-type wrapper components that handle their own data hooks
  switch (module.type) {
    case "course":
      return (
        <CourseModuleContainer
          module={module as import("~/lib/api/types").CourseModule}
          params={paramProps}
        />
      );

    case "feed":
      return (
        <FeedModule
          module={module as import("~/lib/api/types").FeedModule}
          params={paramProps}
        />
      );

    case "events":
      return (
        <EventsModuleContainer
          module={module as import("~/lib/api/types").EventsModule}
          params={paramProps}
        />
      );

    case "discussion":
      return (
        <div className="bg-zinc-900 min-h-screen p-8">
          <h1 className="text-3xl font-bold text-white mb-4">Discussion</h1>
          <p className="text-zinc-400">Discussion module coming soon...</p>
        </div>
      );

    case "links":
      return (
        <LinksModule
          module={module as import("~/lib/api/types").LinksModule}
          params={paramProps}
        />
      );

    default:
      // This should never happen if all module types are handled
      const _exhaustiveCheck: never = module;
      return (
        <div className="bg-zinc-900 min-h-screen p-8">
          <h1 className="text-3xl font-bold text-white mb-4">Unknown Module</h1>
          <p className="text-zinc-400">Module type is not yet supported.</p>
        </div>
      );
  }
}

// --- Wrapper Components ------------------------------------

function CourseModuleContainer({
  module,
  params,
}: {
  module: import("~/lib/api/types").CourseModule;
  params: { groupId: string; cohortId: string; moduleId: string };
}) {
  const courseId = String(module.config.courseId);
  const { data, isLoading, isError } = useCourse(courseId);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-900 text-zinc-400">
        Loading course…
      </div>
    );
  }

  if (isError || !data) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-900 text-red-500">
        Failed to load course.
      </div>
    );
  }

  const courseData: Course = data.data;
  return (
    <CourseModule module={module} courseData={courseData} params={params} />
  );
}

function EventsModuleContainer({
  module,
  params,
}: {
  module: import("~/lib/api/types").EventsModule;
  params: { groupId: string; cohortId: string; moduleId: string };
}) {
  return <EventsModule module={module} params={params} />;
}
