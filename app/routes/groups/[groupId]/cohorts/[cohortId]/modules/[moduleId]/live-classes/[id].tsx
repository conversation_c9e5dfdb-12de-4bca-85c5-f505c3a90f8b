import { Link, useParams } from "react-router";
import type { Route } from "./+types/[id]";
import { ArrowLeft } from "lucide-react";
import {
  useLiveClass,
  useRegisterLiveClass,
  useMyGroups,
} from "~/lib/api/client-queries";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Live Class - Sphere` },
    { name: "description", content: "Live class details" },
  ];
}

export default function LiveClassDetail() {
  const params = useParams();
  const { groupId, cohortId, moduleId, id: liveClassId } = params;

  // Fetch live class data
  const { data: liveClassResponse, isLoading } = useLiveClass(
    groupId!,
    cohortId!,
    moduleId!,
    liveClassId!
  );

  // Fetch group data to get banner image
  const { data: myGroupsResponse } = useMyGroups();
  const group = myGroupsResponse?.groups.byId[groupId!];

  const registerLiveClass = useRegisterLiveClass();

  if (isLoading) {
    return (
      <div className="bg-black min-h-screen flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  if (!liveClassResponse?.success || !liveClassResponse.data) {
    return (
      <div className="bg-black min-h-screen flex items-center justify-center">
        <div className="text-white">Live class not found</div>
      </div>
    );
  }

  const event = liveClassResponse.data;
  const startTime = new Date(event.startAt);
  const endTime = new Date(event.endsAt);
  const now = new Date();
  const isUpcoming = startTime > now;

  const timeString = `${startTime.toLocaleString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  })} - ${endTime.toLocaleString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  })}`;

  const dateString = startTime.toLocaleString("en-US", {
    weekday: "short",
    month: "short",
    day: "numeric",
  });

  // Calculate duration
  const durationMs = endTime.getTime() - startTime.getTime();
  const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
  const durationMinutes = Math.floor(
    (durationMs % (1000 * 60 * 60)) / (1000 * 60)
  );
  const durationString =
    durationHours > 0
      ? `${durationHours} hr ${
          durationMinutes > 0 ? durationMinutes + " min" : ""
        }`
      : `${durationMinutes} min`;

  const handleRSVP = async () => {
    try {
      await registerLiveClass.mutateAsync({
        groupId: groupId!,
        cohortId: cohortId!,
        moduleId: moduleId!,
        liveClassId: liveClassId!,
      });
    } catch (error) {
      console.error("Failed to register for event:", error);
    }
  };

  return (
    <div className="bg-black min-h-screen">
      {/* Header with banner */}
      <div className="relative h-64 bg-gray-900">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: group?.bannerImage
              ? `url(${group.bannerImage})`
              : `linear-gradient(135deg, #1e293b 0%, #0f172a 100%)`,
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-gray-950 via-gray-950/80 to-transparent" />
        </div>

        {/* Back button */}
        <div className="absolute top-6 left-6">
          <Link
            to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
            className="flex items-center gap-2 text-white hover:text-zinc-300 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to Events</span>
          </Link>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-2xl mx-auto px-8 py-12">
        <div className="bg-zinc-900 rounded-lg p-8">
          <h1 className="text-3xl font-semibold text-white mb-8">
            {event.topic}
          </h1>

          <div className="space-y-6 mb-8">
            <div className="flex justify-between">
              <span className="text-zinc-400 text-lg">Time</span>
              <span className="text-white text-lg">{timeString}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-zinc-400 text-lg">Date</span>
              <span className="text-white text-lg">{dateString}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-zinc-400 text-lg">Duration</span>
              <span className="text-white text-lg">{durationString}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-zinc-400 text-lg">Host by</span>
              <span className="text-white text-lg">Slo Studio</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-zinc-400 text-lg">Participants</span>
              <div className="flex items-center gap-3">
                <div className="flex -space-x-2">
                  {[...Array(Math.min(3, event.numOfParticipants))].map(
                    (_, i) => (
                      <div
                        key={i}
                        className="w-8 h-8 rounded-full bg-zinc-600 border-2 border-zinc-900"
                      />
                    )
                  )}
                </div>
                <span className="text-white text-lg">
                  {event.numOfParticipants} Attending
                </span>
              </div>
            </div>
          </div>

          {isUpcoming && !event.isRegistered && (
            <button
              onClick={handleRSVP}
              disabled={registerLiveClass.isPending}
              className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-600/50 text-white font-medium py-4 rounded-lg transition-colors disabled:cursor-not-allowed text-lg"
            >
              {registerLiveClass.isPending ? "Registering..." : "RSVP"}
            </button>
          )}
          {event.isRegistered && (
            <div className="text-center text-green-400 font-medium text-lg">
              ✓ You're registered for this event
            </div>
          )}
          {!isUpcoming && !event.isRegistered && (
            <div className="text-center text-zinc-400 text-lg">
              This event has already passed
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
